class FitBitConfig {
  static const String clientId = '23QK65';
  static const String clientSecret = '45570c474173d27efa7aac6b7738ca99';

  static const String redirectUri = 'com.techolab.helthy://fitbit/callback';

  static const List<String> scopes = [
    'activity',    // Steps, distance, calories, active minutes
    'heartrate',   // Heart rate data
    'location',    // GPS and location data
    'nutrition',   // Food and water logging
    'profile',     // User profile information
    'settings',    // User preferences and settings
    'sleep',       // Sleep data and analysis
    'social',      // Friends and social features
    'weight',      // Weight and body measurements
  ];

  // API endpoints
  static const String baseUrl = 'https://api.fitbit.com';
  static const String authUrl = 'https://www.fitbit.com/oauth2/authorize';
  static const String tokenUrl = 'https://api.fitbit.com/oauth2/token';

  // Validation
  static bool get isConfigured {
    return clientId.isNotEmpty && clientSecret.isNotEmpty;
  }

  // Detailed validation with specific error messages
  static String? validateConfiguration() {
    if (clientId.isEmpty) {
      return 'Client ID is not configured. Please set it in lib/config/fitbit_config.dart';
    }
    if (clientSecret.isEmpty) {
      return 'Client Secret is not configured. Please set it in lib/config/fitbit_config.dart';
    }
    if (redirectUri.isEmpty) {
      return 'Redirect URI is not configured.';
    }
    if (!redirectUri.startsWith('com.techolab.helthy://')) {
      return 'Redirect URI should start with "com.techolab.helthy://" for proper deep linking.';
    }
    return null; // Configuration is valid
  }

  static String get configurationInstructions {
    return '''
To configure FitBit integration:

1. Visit https://dev.fitbit.com/apps/new
2. Create a new FitBit application with these EXACT settings:
   - Application Name: Healo Health App
   - Description: Health tracking and insights app
   - Application Website: https://your-app-website.com
   - Organization: Your Organization Name
   - Organization Website: https://your-organization.com
   - Terms of Service URL: https://your-app-website.com/terms
   - Privacy Policy URL: https://your-app-website.com/privacy
   - OAuth 2.0 Application Type: Client (NOT Personal!)
   - Callback URL: com.techolab.helthy://fitbit/callback
   - Default Access Type: Read & Write

IMPORTANT: Make sure to select "Client" as the OAuth 2.0 Application Type,
NOT "Personal". Personal apps redirect to login pages instead of authorization.

3. After creating the app, copy the Client ID and Client Secret
4. Replace the placeholder values in lib/config/fitbit_config.dart
5. Update the URL scheme in your app configuration files

For iOS (ios/Runner/Info.plist):
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLName</key>
    <string>fitbit-oauth</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>com.techolab.helthy</string>
    </array>
  </dict>
</array>

For Android (android/app/src/main/AndroidManifest.xml):
<activity
    android:name=".MainActivity"
    android:exported="true"
    android:launchMode="singleTop"
    android:theme="@style/LaunchTheme">
    <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="com.techolab.helthy" />
    </intent-filter>
</activity>

TROUBLESHOOTING:
- If you see a login loop, your app is set to "Personal" instead of "Client"
- If redirect doesn't work, check that the callback URL exactly matches
- Make sure your app is approved and not in development mode
''';
  }
}
