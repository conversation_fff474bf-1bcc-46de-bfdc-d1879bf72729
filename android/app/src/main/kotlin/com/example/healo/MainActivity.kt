package com.techolab.helthy

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.util.Log
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterFragmentActivity() {
    private val SAMSUNG_HEALTH_CHANNEL = "samsung_health_channel"
    private val SAMSUNG_HEALTH_PACKAGE = "com.sec.android.app.shealth"

    private lateinit var samsungHealthHandler: SamsungHealthHandler

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Handle the privacy policy intent from Health Connect
        if (intent?.action == "android.intent.action.VIEW_PERMISSION_USAGE") {
            // Open the privacy policy URL directly
            val privacyPolicyUrl = "https://thehelthy.co/privacy-policy"
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(privacyPolicyUrl))
            startActivity(browserIntent)

            // Note: We're not finishing the activity because we want the user
            // to be able to return to the app after viewing the privacy policy
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        Log.d("SamsungHealth", "Configuring Flutter engine with Samsung Health channel")

        // Initialize Samsung Health handler
        samsungHealthHandler = SamsungHealthHandler(this)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SAMSUNG_HEALTH_CHANNEL).setMethodCallHandler { call, result ->
            Log.d("SamsungHealth", "Method channel call received: ${call.method}")
            when (call.method) {
                "isSamsungHealthAvailable" -> {
                    Log.d("SamsungHealth", "Checking Samsung Health availability")
                    result.success(isSamsungHealthInstalled())
                }
                "initialize" -> {
                    Log.d("SamsungHealth", "Initializing Samsung Health")
                    samsungHealthHandler.initialize(result)
                }
                "isHealthDataAvailable" -> {
                    Log.d("SamsungHealth", "Checking health data availability")
                    samsungHealthHandler.isHealthDataAvailable(result)
                }
                "fetchHealthData" -> {
                    Log.d("SamsungHealth", "Fetching health data")
                    samsungHealthHandler.fetchHealthData(result)
                }
                "fetchStepsOnly" -> {
                    Log.d("SamsungHealth", "Fetching steps only")
                    samsungHealthHandler.fetchStepsOnly(result)
                }
                "fetchHeartRateOnly" -> {
                    Log.d("SamsungHealth", "Fetching heart rate only")
                    samsungHealthHandler.fetchHeartRateOnly(result)
                }
                "fetchCaloriesOnly" -> {
                    Log.d("SamsungHealth", "Fetching calories only")
                    samsungHealthHandler.fetchCaloriesOnly(result)
                }
                "fetchSleepData" -> {
                    Log.d("SamsungHealth", "Fetching sleep data")
                    samsungHealthHandler.fetchSleepData(result)
                }
                else -> {
                    Log.d("SamsungHealth", "Method not implemented: ${call.method}")
                    result.notImplemented()
                }
            }
        }

        Log.d("SamsungHealth", "Samsung Health method channel configured successfully")
    }

    private fun isSamsungHealthInstalled(): Boolean {
        return try {
            packageManager.getPackageInfo(SAMSUNG_HEALTH_PACKAGE, 0)
            Log.d("SamsungHealth", "Samsung Health app is installed")
            true
        } catch (e: PackageManager.NameNotFoundException) {
            Log.d("SamsungHealth", "Samsung Health app is not installed")
            false
        }
    }
}
