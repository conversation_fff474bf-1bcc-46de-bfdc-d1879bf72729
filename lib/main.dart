import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/firebase_options.dart';
import 'package:healo/route/router.dart';
import 'package:healo/screens/splash_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/theme_provider.dart';
import 'package:healo/providers/auth_state_manager.dart';
import 'package:healo/providers/fitbit_provider.dart';
import 'package:healo/services/secure_storage_service.dart';
import 'package:healo/services/notification_service.dart';
import 'package:healo/services/navigation_service.dart';
import 'package:app_links/app_links.dart';
import 'dart:async';
import 'dart:developer';

// Global provider container for background access
late ProviderContainer globalProviderContainer;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  FirebaseFirestore.instance.settings =
      const Settings(persistenceEnabled: true);

  // Initialize push notifications
  await NotificationService().initialize();

  // Initialize deep link handling
  await initDeepLinkHandling();

  runApp(
    ProviderScope(
      child: const MyApp(),
    ),
  );
}

// Handle deep links
Future<void> initDeepLinkHandling() async {
  final appLinks = AppLinks();

  // Handle app opened from a link
  try {
    final initialUri = await appLinks.getInitialLink();
    if (initialUri != null) {
      log('App opened with initial URI: $initialUri');
      handleDeepLink(initialUri);
    }
  } catch (e) {
    log('Error getting initial URI: $e');
  }

  // Handle links when app is already running
  appLinks.uriLinkStream.listen((Uri uri) {
    log('Got URI while app running: $uri');
    handleDeepLink(uri);
  }, onError: (error) {
    log('Error handling URI link: $error');
  });
}

// Process deep links
void handleDeepLink(Uri uri) {
  log('Processing deep link: $uri');

  // Check if this is a FitBit callback
  if (uri.toString().startsWith('com.techolab.helthy://fitbit/callback')) {
    log('FitBit callback detected');

    // Extract authorization code
    final code = uri.queryParameters['code'];

    if (code != null) {
      log('Authorization code received: $code');

      // Get the stored code verifier and complete OAuth flow
      _handleFitbitCallback(code);
    } else {
      log('Error: No authorization code in callback');
    }
  }
}

// Handle FitBit OAuth callback
Future<void> _handleFitbitCallback(String authorizationCode) async {
  try {
    final codeVerifier = await SecureStorageService.getFitbitCodeVerifier();

    if (codeVerifier != null) {
      log('Code verifier retrieved, completing OAuth flow');

      final success = await globalProviderContainer
          .read(fitbitProvider.notifier)
          .handleAuthCallback(authorizationCode, codeVerifier);

      if (success) {
        log('FitBit OAuth flow completed successfully');
        await SecureStorageService.clearFitbitCodeVerifier();
      } else {
        log('FitBit OAuth flow failed');
      }
    } else {
      log('Error: No code verifier found');
    }
  } catch (e) {
    log('Error handling FitBit callback: $e');
  }
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    globalProviderContainer = ProviderScope.containerOf(context);
    final isDarkMode = ref.watch(themeModeProvider);

    ref.watch(authStateManagerProvider);

    ref.watch(fitbitProvider);

    MySize().init(context);
    SizeConfig().init(context);

    return MaterialApp(
        title: 'Healo',
        debugShowCheckedModeBanner: false,
        navigatorKey: NavigationService.navigatorKey,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.black),
          scaffoldBackgroundColor: AppColors.white,
          cardColor: AppColors.white100,
          useMaterial3: true,
          appBarTheme: AppBarTheme(
              elevation: 0,
              backgroundColor: AppColors.white,
              foregroundColor: AppColors.black),
          textTheme: TextTheme(
            bodySmall: GoogleFonts.poppins(
              color: AppColors.textGray,
            ),
            bodyMedium: GoogleFonts.poppins(
              color: AppColors.black,
            ),
            bodyLarge: GoogleFonts.poppins(
              color: AppColors.black,
            ),
          ),
        ),
        darkTheme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.black),
            scaffoldBackgroundColor: AppColors.black,
            useMaterial3: true,
            cardColor: AppColors.black100,
            appBarTheme: AppBarTheme(
                elevation: 0,
                backgroundColor: AppColors.black,
                foregroundColor: AppColors.white),
            textTheme: TextTheme(
              bodySmall: GoogleFonts.poppins(
                color: AppColors.white,
              ),
              bodyMedium: GoogleFonts.poppins(
                color: AppColors.white,
              ),
              bodyLarge: GoogleFonts.poppins(
                color: AppColors.white,
              ),
            )),
        themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
        onGenerateRoute: (settings) => generateRoute(settings),
        home: SplashScreen());
  }
}
