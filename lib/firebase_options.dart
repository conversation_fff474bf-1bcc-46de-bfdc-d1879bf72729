// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAT0BNRVlBABw9IN7SLhVGhucYdS4YGJXw',
    appId: '1:480554517469:web:1a54047606d608a62cec34',
    messagingSenderId: '480554517469',
    projectId: 'helthyapi',
    authDomain: 'helthyapi.firebaseapp.com',
    storageBucket: 'helthyapi.firebasestorage.app',
    measurementId: 'G-BS0QEXYD07',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCI8FRWu5Y7cACkDYssW3MSFQEDYoBvI88',
    appId: '1:480554517469:android:01722bdd6be80e352cec34',
    messagingSenderId: '480554517469',
    projectId: 'helthyapi',
    storageBucket: 'helthyapi.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDuOogB0jkKvmjUtSQewwyyJ3256gRxOzM',
    appId: '1:480554517469:ios:a66bff4da7a50cec2cec34',
    messagingSenderId: '480554517469',
    projectId: 'helthyapi',
    storageBucket: 'helthyapi.firebasestorage.app',
    androidClientId: '480554517469-r1e2fvbv82qofb45v0lgl4jpqa0hv1vj.apps.googleusercontent.com',
    iosBundleId: 'com.techolab.helthy',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDuOogB0jkKvmjUtSQewwyyJ3256gRxOzM',
    appId: '1:480554517469:ios:19325dbf9224e5ab2cec34',
    messagingSenderId: '480554517469',
    projectId: 'helthyapi',
    storageBucket: 'helthyapi.firebasestorage.app',
    iosClientId: '480554517469-h6i701cmcdvuo3fostpadgtt4rkpeph0.apps.googleusercontent.com',
    iosBundleId: 'com.techolab.helthy',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAT0BNRVlBABw9IN7SLhVGhucYdS4YGJXw',
    appId: '1:480554517469:web:37e56a075c6f79d42cec34',
    messagingSenderId: '480554517469',
    projectId: 'helthyapi',
    authDomain: 'helthyapi.firebaseapp.com',
    storageBucket: 'helthyapi.firebasestorage.app',
    measurementId: 'G-YRCH2CC0HV',
  );
}