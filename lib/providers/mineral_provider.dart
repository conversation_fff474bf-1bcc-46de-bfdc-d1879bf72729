import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';

// Provider for mineral history data
final mineralHistoryProvider = StateNotifierProvider<MineralHistoryNotifier, Map<String, Map<String, dynamic>>>(
  (ref) => MineralHistoryNotifier(FirestoreService(), ref),
);

class MineralHistoryNotifier extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  MineralHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchMineralHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in mineral history provider: $error");
        },
      );
    });
  }

  // Add a new mineral reading
  Future<void> addMineralReading(String date, Map<String, dynamic> reading) async {
    try {
      await _firestoreService.addMineralReading(date, reading);
      await fetchMineralHistory();
    } catch (e) {
      log("Error adding mineral reading: $e");
      throw Exception("Failed to add mineral reading: $e");
    }
  }

  // Fetch mineral history
  Future<void> fetchMineralHistory() async {
    try {
      final data = await _firestoreService.fetchMineralHistory();
      if (data.containsKey('history')) {
        state = Map<String, Map<String, dynamic>>.from(data['history']);
      } else {
        state = {};
      }
    } catch (e) {
      log("Error fetching mineral history: $e");
      state = {};
    }
  }
}

// Helper function to parse date string
DateTime _parseDate(String dateString) {
  try {
    final parts = dateString.split('-');
    if (parts.length == 3) {
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);
      return DateTime(year, month, day);
    }
  } catch (e) {
    log("Error parsing date: $dateString");
  }
  return DateTime.now();
}

// Provider for latest mineral readings
final latestMineralReadingProvider = Provider<Map<String, dynamic>?>((ref) {
  final mineralData = ref.watch(mineralHistoryProvider);

  if (mineralData.isEmpty) return null;

  final sortedDates = mineralData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  final latestDate = sortedDates.last;
  final readings = mineralData[latestDate]?['readings'] as List<dynamic>?;

  if (readings == null || readings.isEmpty) return null;

  return readings.last as Map<String, dynamic>;
});
