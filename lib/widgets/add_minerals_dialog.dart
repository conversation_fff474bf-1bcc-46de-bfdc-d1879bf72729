import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/vitamins_minerals_provider.dart';

class AddMineralsDialog extends ConsumerStatefulWidget {
  const AddMineralsDialog({super.key});

  @override
  ConsumerState<AddMineralsDialog> createState() => _AddMineralsDialogState();
}

class _AddMineralsDialogState extends ConsumerState<AddMineralsDialog> {
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, String> _mineralUnits = {
    'Calcium (Total)': 'mg/dL',
    'Iron (Serum)': 'μg/dL',
    'Ferritin': 'ng/mL',
    'Magnesium': 'mg/dL',
    'Phosphorus': 'mg/dL',
    'Zinc': 'μg/dL',
    'Selenium': 'μg/dL',
    'Copper': 'μg/dL',
  };

  @override
  void initState() {
    super.initState();
    for (String mineral in _mineralUnits.keys) {
      _controllers[mineral] = TextEditingController();
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(vitaminsAndMineralsProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(MySize.size16),
      ),
      child: Container(
        width: double.maxFinite,
        height: MySize.safeHeight! * 0.7,
        padding: EdgeInsets.all(MySize.size20),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Add Mineral Values",
              style: TextStyle(
                fontSize: MySize.size20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            SizedBox(height: MySize.size20),

            // GridView takes all available space now
            Expanded(
              child: GridView.builder(
                padding: EdgeInsets.zero,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 2.5,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 24,
                ),
                itemCount: _mineralUnits.length,
                itemBuilder: (context, index) {
                  final mineral = _mineralUnits.keys.elementAt(index);
                  final unit = _mineralUnits[mineral]!;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        mineral,
                        style: TextStyle(
                          fontSize: MySize.size14,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                      SizedBox(height: MySize.size8),
                      Container(
                        height: MySize.size49,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey.shade300,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(MySize.size8),
                        ),
                        child: TextField(
                          controller: _controllers[mineral],
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: MySize.size14),
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: MySize.size8,
                              vertical: MySize.size4,
                            ),
                            suffixText: unit,
                            suffixStyle: TextStyle(
                              fontSize: MySize.size12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),

            SizedBox(height: MySize.size24),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: MySize.size12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size25),
                      ),
                      side: BorderSide(color: Colors.grey.shade400),
                    ),
                    child: Text(
                      "Cancel",
                      style: TextStyle(
                        fontSize: MySize.size16,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: MySize.size16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: state.isLoading ? null : _saveMinerals,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      padding: EdgeInsets.symmetric(vertical: MySize.size12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size25),
                      ),
                    ),
                    child: state.isLoading
                        ? SizedBox(
                            height: MySize.size20,
                            width: MySize.size20,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            "Save Results",
                            style: TextStyle(
                              fontSize: MySize.size16,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _saveMinerals() async {
    try {
      final mineralData = <String, dynamic>{};

      for (String mineral in _mineralUnits.keys) {
        final value = _controllers[mineral]?.text.trim();
        if (value != null && value.isNotEmpty) {
          mineralData[mineral] = value;
        }
      }

      if (mineralData.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Please enter at least one mineral value"),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      await ref
          .read(vitaminsAndMineralsProvider.notifier)
          .addMineralReading(mineralData);

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Mineral values saved successfully!"),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Error saving mineral values: $e"),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
