import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/vitamin_provider.dart';
import 'package:healo/providers/mineral_provider.dart';
import 'package:healo/providers/vitamins_minerals_provider.dart';
import 'package:healo/widgets/add_vitamins_dialog.dart';
import 'package:healo/widgets/add_minerals_dialog.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class VitaminsMineralsScreen extends ConsumerStatefulWidget {
  const VitaminsMineralsScreen({super.key});

  @override
  ConsumerState<VitaminsMineralsScreen> createState() =>
      _VitaminsMineralsScreenState();
}

class _VitaminsMineralsScreenState
    extends ConsumerState<VitaminsMineralsScreen> {
  bool isVitaminsSelected = true;

  @override
  void initState() {
    super.initState();
    // Fetch vitamin and mineral data when screen loads
    Future.microtask(() {
      ref.read(vitaminHistoryProvider.notifier).fetchVitaminHistory();
      ref.read(vitaminIntakeProvider.notifier).fetchVitaminIntake();
      ref.read(mineralHistoryProvider.notifier).fetchMineralHistory();
      ref.read(vitaminsAndMineralsProvider.notifier).refreshData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final latestVitaminReading = ref.watch(latestVitaminReadingProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "Vitamins & Minerals",
        actions: [
          IconButton(
            onPressed: _showAddDialog,
            icon: Icon(
              Icons.add,
              color: AppColors.primaryColor,
              size: MySize.size24,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(MySize.size15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Nutrition Score Gauge
              _buildNutritionScoreGauge(),

              Space.height(20),

              // Recommendation Card
              _buildRecommendationCard(),

              Space.height(20),

              // Toggle Switch
              _buildToggleSwitch(),

              Space.height(20),

              // Nutrient Categories
              _buildNutrientCategories(latestVitaminReading),

              // Recommendations
              Space.height(20),
              _buildRecommendationsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNutritionScoreGauge() {
    final latestVitaminData = ref.watch(latestVitaminReadingProvider);
    final latestMineralData = ref.watch(latestMineralReadingProvider);

    // Check if any data exists
    bool hasData = latestVitaminData != null || latestMineralData != null;

    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size15),
        boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
      ),
      child: Column(
        children: [
          Text(
            "Nutrition Score",
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          Space.height(MySize.size30),
          SizedBox(
            height: MySize.size150,
            child: hasData ? SfRadialGauge(
              enableLoadingAnimation: true,
              axes: <RadialAxis>[
                RadialAxis(
                  showLabels: false,
                  showTicks: false,
                  startAngle: 180,
                  endAngle: 0,
                  radiusFactor: 1.25,
                  minimum: 0,
                  maximum: 100,
                  axisLineStyle: AxisLineStyle(
                    thicknessUnit: GaugeSizeUnit.factor,
                    thickness: 0.25,
                    color: AppColors.progressBackground.withAlpha(50),
                  ),
                  pointers: <GaugePointer>[
                    RangePointer(
                      value: _calculateNutritionScore(),
                      enableAnimation: true,
                      animationDuration: 1200,
                      sizeUnit: GaugeSizeUnit.factor,
                      color: AppColors.primaryColor,
                      width: 0.25,
                    ),
                  ],
                  annotations: <GaugeAnnotation>[
                    GaugeAnnotation(
                      angle: 90,
                      positionFactor: 0,
                      widget: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            "${_calculateNutritionScore().round()}",
                            style: TextStyle(
                              fontSize: MySize.size36,
                              fontWeight: FontWeight.bold,
                              color:
                                  Theme.of(context).textTheme.bodyLarge?.color,
                            ),
                          ),
                          Text(
                            _getNutritionScoreLabel(_calculateNutritionScore()),
                            style: TextStyle(
                              fontSize: MySize.size16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ) : Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    size: MySize.size48,
                    color: Colors.grey.shade400,
                  ),
                  SizedBox(height: MySize.size12),
                  Text(
                    "No Data",
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  SizedBox(height: MySize.size4),
                  Text(
                    "Add vitamins or minerals to see your score",
                    style: TextStyle(
                      fontSize: MySize.size12,
                      color: Colors.grey.shade500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard() {
    final latestVitaminData = ref.watch(latestVitaminReadingProvider);
    final latestMineralData = ref.watch(latestMineralReadingProvider);

    // Get dynamic recommendation based on data
    String recommendation = _getDynamicRecommendation(latestVitaminData, latestMineralData);
    Color cardColor = _getRecommendationColor(latestVitaminData, latestMineralData);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size10),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: SvgPicture.asset( isVitaminsSelected ?
              "assets/svg/vitamin_icon.svg" : "assets/svg/mineral_icon.svg",
              height: MySize.size24,
              width: MySize.size24,
              colorFilter: ColorFilter.mode(
                Colors.white,
                BlendMode.srcIn,
              ),
            ),
          ),
          Space.width(12),
          Expanded(
            child: Text(
              recommendation,
              style: TextStyle(
                fontSize: MySize.size13,
                color: Theme.of(context).textTheme.bodySmall!.color,
                fontWeight: FontWeight.w500,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSwitch() {
    return Container(
      padding: EdgeInsets.all(MySize.size10),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size8),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isVitaminsSelected = true;
                });
              },
              child: AnimatedContainer(
                duration: Duration(milliseconds: 200),
                height: MySize.size45,
                decoration: BoxDecoration(
                  color: isVitaminsSelected
                      ? AppColors.primaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Center(
                  child: Text(
                    'Vitamins',
                    style: TextStyle(
                      color: isVitaminsSelected
                          ? Colors.white
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      fontWeight: FontWeight.w600,
                      fontSize: MySize.size14,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isVitaminsSelected = false;
                });
              },
              child: AnimatedContainer(
                duration: Duration(milliseconds: 200),
                height: MySize.size45,
                decoration: BoxDecoration(
                  color: !isVitaminsSelected
                      ? AppColors.primaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Center(
                  child: Text(
                    'Minerals',
                    style: TextStyle(
                      color: !isVitaminsSelected
                          ? Colors.white
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      fontWeight: FontWeight.w600,
                      fontSize: MySize.size14,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutrientCategories(Map<String, dynamic>? latestReading) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isVitaminsSelected ? "Nutrient Categories" : "Minerals Categories",
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        Space.height(15),
        if (isVitaminsSelected)
          _buildVitaminsList(latestReading)
        else
          _buildMineralsList(latestReading),
      ],
    );
  }

  Widget _buildVitaminsList(Map<String, dynamic>? latestReading) {
    final latestVitaminData = ref.watch(latestVitaminReadingProvider);
    final vitaminsData = latestVitaminData?['vitamins'] as Map<String, dynamic>?;

    // Helper function to get real value or show 0 if no data exists
    String getVitaminValue(String vitaminName, String fallbackValue) {
      if (latestVitaminData == null) {
        return "0"; // No data exists in Firebase
      }
      return vitaminsData?[vitaminName]?.toString() ?? "0";
    }

    final vitamins = [
      {
        'name': 'Vitamin A',
        'value': getVitaminValue('Vitamin A', '400'),
        'unit': 'μg/L',
        'color': Colors.purple,
        'progress': 0.8
      },
      {
        'name': 'Vitamin B1',
        'value': getVitaminValue('Vitamin B1', '75'),
        'unit': 'μg/L',
        'color': Colors.orange,
        'progress': 0.6
      },
      {
        'name': 'Vitamin B2',
        'value': getVitaminValue('Vitamin B2', '1.4'),
        'unit': 'μg/L',
        'color': Colors.blue,
        'progress': 0.7
      },
      {
        'name': 'Vitamin B3',
        'value': getVitaminValue('Vitamin B3', '1.2'),
        'unit': 'μg/L',
        'color': Colors.cyan,
        'progress': 0.5
      },
      {
        'name': 'Vitamin B5',
        'value': getVitaminValue('Vitamin B5', '1.6'),
        'unit': 'μg/L',
        'color': Colors.green,
        'progress': 0.8
      },
      {
        'name': 'Vitamin B6',
        'value': getVitaminValue('Vitamin B6', '5-50'),
        'unit': 'μg/mL',
        'color': Colors.indigo,
        'progress': 0.6
      },
      {
        'name': 'Vitamin B7',
        'value': getVitaminValue('Vitamin B7', '250'),
        'unit': 'μg/L',
        'color': Colors.red,
        'progress': 0.7
      },
      {
        'name': 'Vitamin B9',
        'value': getVitaminValue('Vitamin B9', '2.7'),
        'unit': 'μg/mL',
        'color': Colors.teal,
        'progress': 0.4
      },
      {
        'name': 'Vitamin B12',
        'value': getVitaminValue('Vitamin B12', '197'),
        'unit': 'pg/mL',
        'color': Colors.blue[700]!,
        'progress': 0.8
      },
      {
        'name': 'Vitamin C',
        'value': getVitaminValue('Vitamin C', '0.8'),
        'unit': 'mL',
        'color': Colors.orange[600]!,
        'progress': 0.4
      },
      {
        'name': 'Vitamin D (25-OH)',
        'value': getVitaminValue('Vitamin D (25-OH)', '30'),
        'unit': 'ng/mL',
        'color': Colors.green[500]!,
        'progress': 0.6
      },
      {
        'name': 'Vitamin E',
        'value': getVitaminValue('Vitamin E', '5.5'),
        'unit': 'mg/L',
        'color': Colors.brown[600]!,
        'progress': 0.7
      },
      {
        'name': 'Vitamin K1',
        'value': getVitaminValue('Vitamin K1', '3.1'),
        'unit': 'ng/mL',
        'color': Colors.yellow[700]!,
        'progress': 0.5
      },
    ];

    return Column(
      children: [
        ...vitamins
            .map((vitamin) => _buildNutrientItem(
                  name: vitamin['name'] as String,
                  value: vitamin['value'] as String,
                  unit: vitamin['unit'] as String,
                  color: vitamin['color'] as Color,
                  progress: vitamin['progress'] as double,
                )),
      ],
    );
  }

  Widget _buildMineralsList(Map<String, dynamic>? latestReading) {
    final latestMineralData = ref.watch(latestMineralReadingProvider);
    final mineralsData = latestMineralData?['minerals'] as Map<String, dynamic>?;

    // Helper function to get real value or show 0 if no data exists
    String getMineralValue(String mineralName, String fallbackValue) {
      if (latestMineralData == null) {
        return "0"; // No data exists in Firebase
      }
      return mineralsData?[mineralName]?.toString() ?? "0";
    }

    final minerals = [
      {
        'name': 'Calcium (Total)',
        'value': getMineralValue('Calcium (Total)', '8.6'),
        'unit': 'mg/dL',
        'color': Colors.green,
        'progress': 0.7
      },
      {
        'name': 'Iron (Serum)',
        'value': getMineralValue('Iron (Serum)', '60'),
        'unit': 'μg/dL',
        'color': Colors.orange,
        'progress': 0.5
      },
      {
        'name': 'Ferritin',
        'value': getMineralValue('Ferritin', 'Male 30'),
        'unit': 'μg/mL',
        'color': Colors.cyan,
        'progress': 0.6
      },
      {
        'name': 'Magnesium',
        'value': getMineralValue('Magnesium', '1.8'),
        'unit': 'mg/dL',
        'color': Colors.blue,
        'progress': 0.4
      },
      {
        'name': 'Phosphorus',
        'value': getMineralValue('Phosphorus', '2.5'),
        'unit': 'mg/dL',
        'color': Colors.red,
        'progress': 0.3
      },
      {
        'name': 'Zinc',
        'value': getMineralValue('Zinc', '70'),
        'unit': 'μg/dL',
        'color': Colors.indigo,
        'progress': 0.8
      },
      {
        'name': 'Selenium',
        'value': getMineralValue('Selenium', '70'),
        'unit': 'μg/L',
        'color': Colors.blue,
        'progress': 0.8
      },
      {
        'name': 'Copper',
        'value': getMineralValue('Copper', 'Male 35'),
        'unit': 'μg/dL',
        'color': Colors.green,
        'progress': 0.6
      },
      {
        'name': 'Manganese',
        'value': getMineralValue('Manganese', '4.7'),
        'unit': 'μg/L',
        'color': Colors.green[600]!,
        'progress': 0.7
      },
      {
        'name': 'Iodine(urine)',
        'value': getMineralValue('Iodine(urine)', '120'),
        'unit': 'μg/L',
        'color': Colors.yellow[700]!,
        'progress': 0.6
      },
      {
        'name': 'Sodium(Na+)',
        'value': getMineralValue('Sodium(Na+)', '135'),
        'unit': 'mmol/L',
        'color': Colors.purple[600]!,
        'progress': 0.8
      },
      {
        'name': 'Potassium(K+)',
        'value': getMineralValue('Potassium(K+)', '3.5'),
        'unit': 'mmol/L',
        'color': Colors.red[500]!,
        'progress': 0.4
      },
      {
        'name': 'Chloride (cl-)',
        'value': getMineralValue('Chloride (cl-)', '98'),
        'unit': 'mmol/L',
        'color': Colors.blue[600]!,
        'progress': 0.7
      },
      {
        'name': 'Fluoride',
        'value': getMineralValue('Fluoride', '1.5'),
        'unit': 'mg/L',
        'color': Colors.teal[700]!,
        'progress': 0.8
      },
      {
        'name': 'Chromium',
        'value': getMineralValue('Chromium', '0.2'),
        'unit': 'μg/L',
        'color': Colors.purple[400]!,
        'progress': 0.9
      },
      {
        'name': 'Molybdenum',
        'value': getMineralValue('Molybdenum', '0.45'),
        'unit': 'μg/L',
        'color': Colors.orange[600]!,
        'progress': 0.5
      },
    ];

    return Column(
      children: [
        ...minerals
            .map((mineral) => _buildNutrientItem(
                  name: mineral['name'] as String,
                  value: mineral['value'] as String,
                  unit: mineral['unit'] as String,
                  color: mineral['color'] as Color,
                  progress: mineral['progress'] as double,
                )),
      ],
    );
  }

  Widget _buildRecommendationsSection() {
    final latestVitaminData = ref.watch(latestVitaminReadingProvider);
    final latestMineralData = ref.watch(latestMineralReadingProvider);

    // Get dynamic food recommendations
    List<Map<String, dynamic>> recommendations = _getDynamicFoodRecommendations(latestVitaminData, latestMineralData);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Recommendations",
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        Space.height(15),
        ...recommendations.map((rec) => Column(
          children: [
            _buildFoodRecommendationCard(
              icon: rec['icon'] as IconData,
              title: rec['title'] as String,
              description: rec['description'] as String,
            ),
            if (rec != recommendations.last) Space.height(15),
          ],
        )),
      ],
    );
  }

  Widget _buildFoodRecommendationCard({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size10),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(MySize.size10),
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: MySize.size24,
            ),
          ),
          SizedBox(width: MySize.size15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: MySize.size16,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutrientItem({
    required String name,
    required String value,
    required String unit,
    required Color color,
    required double progress,
  }) {
    // Check if the value is "0" or empty to determine if we should show progress
    bool hasValue = value != "0" && value.isNotEmpty;
    double actualProgress = hasValue ? progress : 0.0;

    return Container(
      margin: EdgeInsets.only(bottom: MySize.size15),
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(MySize.size12),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
              Text(
                "$value $unit",
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.bold,
                  color: hasValue
                      ? Theme.of(context).textTheme.bodyLarge?.color
                      : Colors.grey.shade500,
                ),
              ),
            ],
          ),
          Space.height(8),
          LinearProgressIndicator(
            value: actualProgress,
            backgroundColor: hasValue
                ? color.withAlpha(50)
                : Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(
              hasValue ? color : Colors.grey.shade300
            ),
            minHeight: MySize.size10,
            borderRadius: BorderRadius.circular(MySize.size8),
          ),
        ],
      ),
    );
  }

  void _showAddDialog() {
    if (isVitaminsSelected) {
      showDialog(
        context: context,
        builder: (context) => const AddVitaminsDialog(),
      );
    } else {
      showDialog(
        context: context,
        builder: (context) => const AddMineralsDialog(),
      );
    }
  }

  // Calculate nutrition score based on available vitamin and mineral data
  double _calculateNutritionScore() {
    final latestVitaminData = ref.watch(latestVitaminReadingProvider);
    final latestMineralData = ref.watch(latestMineralReadingProvider);

    // Default score if no data
    if (latestVitaminData == null && latestMineralData == null) {
      return 0.0;
    }

    // Count how many vitamins and minerals have values
    int totalNutrients = 0;
    int nutrientsInRange = 0;

    // Check vitamins
    if (latestVitaminData != null && latestVitaminData['vitamins'] is Map<String, dynamic>) {
      final vitaminsData = latestVitaminData['vitamins'] as Map<String, dynamic>;
      totalNutrients += vitaminsData.length;

      // Count vitamins in healthy range (simplified logic)
      for (var value in vitaminsData.values) {
        if (value != null && value.toString() != "0") {
          nutrientsInRange++;
        }
      }
    }

    // Check minerals
    if (latestMineralData != null && latestMineralData['minerals'] is Map<String, dynamic>) {
      final mineralsData = latestMineralData['minerals'] as Map<String, dynamic>;
      totalNutrients += mineralsData.length;

      // Count minerals in healthy range (simplified logic)
      for (var value in mineralsData.values) {
        if (value != null && value.toString() != "0") {
          nutrientsInRange++;
        }
      }
    }

    // Calculate score (0-100)
    if (totalNutrients == 0) return 50.0; // Default score if no nutrients

    return (nutrientsInRange / totalNutrients) * 100;
  }

  // Get label based on nutrition score
  String _getNutritionScoreLabel(double score) {
    if (score >= 80) return "Excellent";
    if (score >= 60) return "Good";
    if (score >= 40) return "Fair";
    if (score >= 20) return "Poor";
    return "Very Poor";
  }

  // Get dynamic recommendation based on available data
  String _getDynamicRecommendation(Map<String, dynamic>? vitaminData, Map<String, dynamic>? mineralData) {
    // If no data exists
    if (vitaminData == null && mineralData == null) {
      return "Add your vitamin and mineral test results to get personalized recommendations";
    }

    // Check for specific deficiencies and provide recommendations
    if (isVitaminsSelected) {
      if (vitaminData != null && vitaminData['vitamins'] is Map<String, dynamic>) {
        final vitamins = vitaminData['vitamins'] as Map<String, dynamic>;

        // Check for Vitamin D deficiency
        final vitaminD = vitamins['Vitamin D (25-OH)'];
        if (vitaminD != null && _isLowValue(vitaminD.toString(), 30)) {
          return "Your Vitamin D is Low. Try to spend 15 mins in sunlight today or consider supplements";
        }

        // Check for Vitamin B12 deficiency
        final vitaminB12 = vitamins['Vitamin B12'];
        if (vitaminB12 != null && _isLowValue(vitaminB12.toString(), 200)) {
          return "Your Vitamin B12 is Low. Include more fish, meat, or fortified cereals in your diet";
        }

        // Check for Vitamin C deficiency
        final vitaminC = vitamins['Vitamin C'];
        if (vitaminC != null && _isLowValue(vitaminC.toString(), 1)) {
          return "Your Vitamin C is Low. Add more citrus fruits, berries, and leafy greens to your meals";
        }

        return "Your vitamin levels look good! Keep maintaining a balanced diet with variety";
      }
      return "Add vitamin test results to get specific recommendations";
    } else {
      if (mineralData != null && mineralData['minerals'] is Map<String, dynamic>) {
        final minerals = mineralData['minerals'] as Map<String, dynamic>;

        // Check for Calcium deficiency
        final calcium = minerals['Calcium (Total)'];
        if (calcium != null && _isLowValue(calcium.toString(), 8.5)) {
          return "Your Calcium level is low. Try adding a glass of milk or dairy greens to your meals today";
        }

        // Check for Iron deficiency
        final iron = minerals['Iron (Serum)'];
        if (iron != null && _isLowValue(iron.toString(), 60)) {
          return "Your Iron level is low. Include more spinach, red meat, or iron-rich foods in your diet";
        }

        // Check for Magnesium deficiency
        final magnesium = minerals['Magnesium'];
        if (magnesium != null && _isLowValue(magnesium.toString(), 1.7)) {
          return "Your Magnesium is low. Add nuts, seeds, and whole grains to boost your levels";
        }

        return "Your mineral levels look good! Continue with your current diet and lifestyle";
      }
      return "Add mineral test results to get specific recommendations";
    }
  }

  // Get recommendation card color based on data status
  Color _getRecommendationColor(Map<String, dynamic>? vitaminData, Map<String, dynamic>? mineralData) {
    if (vitaminData == null && mineralData == null) {
      return Colors.grey.shade600; // No data
    }

    double score = _calculateNutritionScore();
    if (score >= 70) return Color(0xFF10B981); // Green - Good
    if (score >= 50) return Colors.orange; // Orange - Fair
    return Colors.red; // Red - Poor
  }

  // Helper method to check if a value is considered low
  bool _isLowValue(String valueStr, double threshold) {
    try {
      double value = double.parse(valueStr);
      return value < threshold;
    } catch (e) {
      return false; // If can't parse, assume it's not low
    }
  }

  // Get dynamic food recommendations based on data
  List<Map<String, dynamic>> _getDynamicFoodRecommendations(Map<String, dynamic>? vitaminData, Map<String, dynamic>? mineralData) {
    List<Map<String, dynamic>> recommendations = [];

    // If no data exists, show general recommendations
    if (vitaminData == null && mineralData == null) {
      return [
        {
          'icon': Icons.add_circle_outline,
          'title': 'Add Test Results',
          'description': 'Upload your vitamin and mineral test results to get personalized food recommendations',
        },
        {
          'icon': Icons.restaurant_outlined,
          'title': 'Balanced Diet',
          'description': 'Maintain a balanced diet with fruits, vegetables, and whole grains',
        },
      ];
    }

    if (isVitaminsSelected && vitaminData != null && vitaminData['vitamins'] is Map<String, dynamic>) {
      final vitamins = vitaminData['vitamins'] as Map<String, dynamic>;

      // Vitamin D recommendations
      final vitaminD = vitamins['Vitamin D (25-OH)'];
      if (vitaminD != null && _isLowValue(vitaminD.toString(), 30)) {
        recommendations.add({
          'icon': Icons.wb_sunny_outlined,
          'title': 'Salmon & Sunlight',
          'description': 'Rich in Vitamin D. Spend 15 mins in sunlight daily',
        });
      }

      // Vitamin B12 recommendations
      final vitaminB12 = vitamins['Vitamin B12'];
      if (vitaminB12 != null && _isLowValue(vitaminB12.toString(), 200)) {
        recommendations.add({
          'icon': Icons.set_meal_outlined,
          'title': 'Fish & Meat',
          'description': 'Include salmon, tuna, and lean meats for B12',
        });
      }

      // Vitamin C recommendations
      final vitaminC = vitamins['Vitamin C'];
      if (vitaminC != null && _isLowValue(vitaminC.toString(), 1)) {
        recommendations.add({
          'icon': Icons.local_florist_outlined,
          'title': 'Citrus Fruits',
          'description': 'Add oranges, berries, and bell peppers to your diet',
        });
      }

      // If no deficiencies, show general vitamin foods
      if (recommendations.isEmpty) {
        recommendations.addAll([
          {
            'icon': Icons.wb_sunny_outlined,
            'title': 'Salmon',
            'description': 'Rich in Omega-3 and Vitamin D',
          },
          {
            'icon': Icons.eco_outlined,
            'title': 'Leafy Greens',
            'description': 'Spinach and kale for multiple vitamins',
          },
        ]);
      }
    } else if (!isVitaminsSelected && mineralData != null && mineralData['minerals'] is Map<String, dynamic>) {
      final minerals = mineralData['minerals'] as Map<String, dynamic>;

      // Calcium recommendations
      final calcium = minerals['Calcium (Total)'];
      if (calcium != null && _isLowValue(calcium.toString(), 8.5)) {
        recommendations.add({
          'icon': Icons.local_drink_outlined,
          'title': 'Dairy Products',
          'description': 'Milk, yogurt, and cheese for calcium boost',
        });
      }

      // Iron recommendations
      final iron = minerals['Iron (Serum)'];
      if (iron != null && _isLowValue(iron.toString(), 60)) {
        recommendations.add({
          'icon': Icons.restaurant_outlined,
          'title': 'Iron-Rich Foods',
          'description': 'Spinach, red meat, and lentils for iron',
        });
      }

      // Magnesium recommendations
      final magnesium = minerals['Magnesium'];
      if (magnesium != null && _isLowValue(magnesium.toString(), 1.7)) {
        recommendations.add({
          'icon': Icons.grain_outlined,
          'title': 'Nuts & Seeds',
          'description': 'Almonds, pumpkin seeds for magnesium',
        });
      }

      // If no deficiencies, show general mineral foods
      if (recommendations.isEmpty) {
        recommendations.addAll([
          {
            'icon': Icons.local_drink_outlined,
            'title': 'Dairy & Greens',
            'description': 'Calcium-rich foods for strong bones',
          },
          {
            'icon': Icons.restaurant_outlined,
            'title': 'Lean Proteins',
            'description': 'Iron and zinc from quality protein sources',
          },
        ]);
      }
    } else {
      // Default recommendations when no specific data for selected tab
      recommendations.addAll([
        {
          'icon': isVitaminsSelected ? Icons.wb_sunny_outlined : Icons.local_drink_outlined,
          'title': isVitaminsSelected ? 'Vitamin-Rich Foods' : 'Mineral-Rich Foods',
          'description': isVitaminsSelected
              ? 'Add colorful fruits and vegetables to your diet'
              : 'Include dairy, nuts, and lean proteins',
        },
        {
          'icon': Icons.eco_outlined,
          'title': 'Balanced Nutrition',
          'description': 'Maintain variety in your daily meals',
        },
      ]);
    }

    if (recommendations.length < 2) {
      recommendations.add({
        'icon': Icons.fitness_center_outlined,
        'title': 'Stay Active',
        'description': 'Regular exercise helps nutrient absorption',
      });
    }

    return recommendations.take(2).toList();
  }
}
