import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/vitamin_provider.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class VitaminsMineralsScreen extends ConsumerStatefulWidget {
  const VitaminsMineralsScreen({super.key});

  @override
  ConsumerState<VitaminsMineralsScreen> createState() =>
      _VitaminsMineralsScreenState();
}

class _VitaminsMineralsScreenState
    extends ConsumerState<VitaminsMineralsScreen> {
  bool isVitaminsSelected = true;

  @override
  void initState() {
    super.initState();
    // Fetch vitamin data when screen loads
    Future.microtask(() {
      ref.read(vitaminHistoryProvider.notifier).fetchVitaminHistory();
      ref.read(vitaminIntakeProvider.notifier).fetchVitaminIntake();
    });
  }

  @override
  Widget build(BuildContext context) {
    final latestVitaminReading = ref.watch(latestVitaminReadingProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "Vitamins & Minerals",
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Navigate to add vitamin/mineral reading screen
            },
            icon: Icon(
              Icons.add,
              color: AppColors.primaryColor,
              size: MySize.size24,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(MySize.size15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Nutrition Score Gauge
              _buildNutritionScoreGauge(),

              Space.height(20),

              // Recommendation Card
              _buildRecommendationCard(),

              Space.height(20),

              // Toggle Switch
              _buildToggleSwitch(),

              Space.height(20),

              // Nutrient Categories
              _buildNutrientCategories(latestVitaminReading),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNutritionScoreGauge() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size15),
        boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
      ),
      child: Column(
        children: [
          Text(
            "Nutrition Score",
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          Space.height(MySize.size30),
          SizedBox(
            height: MySize.size150,
            child: SfRadialGauge(
              enableLoadingAnimation: true,
              axes: <RadialAxis>[
                RadialAxis(
                  showLabels: false,
                  showTicks: false,
                  startAngle: 180,
                  endAngle: 0,
                  radiusFactor: 1.25,
                  minimum: 0,
                  maximum: 100,
                  axisLineStyle: AxisLineStyle(
                    thicknessUnit: GaugeSizeUnit.factor,
                    thickness: 0.25,
                    color: AppColors.progressBackground.withAlpha(50),
                  ),
                  pointers: <GaugePointer>[
                    RangePointer(
                      value: 78,
                      enableAnimation: true,
                      animationDuration: 1200,
                      sizeUnit: GaugeSizeUnit.factor,
                      color: AppColors.primaryColor,
                      width: 0.25,
                    ),
                  ],
                  annotations: <GaugeAnnotation>[
                    GaugeAnnotation(
                      angle: 90,
                      positionFactor: 0,
                      widget: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            "78",
                            style: TextStyle(
                              fontSize: MySize.size36,
                              fontWeight: FontWeight.bold,
                              color:
                                  Theme.of(context).textTheme.bodyLarge?.color,
                            ),
                          ),
                          Text(
                            "Good",
                            style: TextStyle(
                              fontSize: MySize.size16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size10),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: Color(0xFF10B981), // Green color
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: SvgPicture.asset( isVitaminsSelected ?
              "assets/svg/vitamin_icon.svg" : "assets/svg/mineral_icon.svg",
              height: MySize.size24,
              width: MySize.size24,
              colorFilter: ColorFilter.mode(
                Colors.white,
                BlendMode.srcIn,
              ),
            ),
          ),
          Space.width(12),
          Expanded(
            child: Text(
              isVitaminsSelected
                  ? "Your Vitamin D is Low. Try to Spend 15 mins in Sunlight Today"
                  : "Your Calcium level is low. Try adding a glass of milk, or dairy greens to your meals today",
              style: TextStyle(
                fontSize: MySize.size13,
                color: Theme.of(context).textTheme.bodySmall!.color,
                fontWeight: FontWeight.w500,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSwitch() {
    return Container(
      padding: EdgeInsets.all(MySize.size10),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size8),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isVitaminsSelected = true;
                });
              },
              child: AnimatedContainer(
                duration: Duration(milliseconds: 200),
                height: MySize.size45,
                decoration: BoxDecoration(
                  color: isVitaminsSelected
                      ? AppColors.primaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Center(
                  child: Text(
                    'Vitamins',
                    style: TextStyle(
                      color: isVitaminsSelected
                          ? Colors.white
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      fontWeight: FontWeight.w600,
                      fontSize: MySize.size14,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isVitaminsSelected = false;
                });
              },
              child: AnimatedContainer(
                duration: Duration(milliseconds: 200),
                height: MySize.size45,
                decoration: BoxDecoration(
                  color: !isVitaminsSelected
                      ? AppColors.primaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Center(
                  child: Text(
                    'Minerals',
                    style: TextStyle(
                      color: !isVitaminsSelected
                          ? Colors.white
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      fontWeight: FontWeight.w600,
                      fontSize: MySize.size14,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutrientCategories(Map<String, dynamic>? latestReading) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isVitaminsSelected ? "Nutrient Categories" : "Minerals Categories",
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        Space.height(15),
        if (isVitaminsSelected)
          _buildVitaminsList(latestReading)
        else
          _buildMineralsList(latestReading),
      ],
    );
  }

  Widget _buildVitaminsList(Map<String, dynamic>? latestReading) {
    final vitamins = [
      {
        'name': 'Vitamin A',
        'value': '400',
        'unit': 'μg/L',
        'color': Colors.purple,
        'progress': 0.8
      },
      {
        'name': 'Vitamin B1',
        'value': '75',
        'unit': 'μg/L',
        'color': Colors.orange,
        'progress': 0.6
      },
      {
        'name': 'Vitamin B2',
        'value': '1.4',
        'unit': 'μg/L',
        'color': Colors.blue,
        'progress': 0.7
      },
      {
        'name': 'Vitamin B3',
        'value': '1.2',
        'unit': 'μg/L',
        'color': Colors.cyan,
        'progress': 0.5
      },
      {
        'name': 'Vitamin B5',
        'value': '1.6',
        'unit': 'μg/L',
        'color': Colors.green,
        'progress': 0.8
      },
      {
        'name': 'Vitamin B6',
        'value': '5-50',
        'unit': 'μg/mL',
        'color': Colors.indigo,
        'progress': 0.6
      },
      {
        'name': 'Vitamin B7',
        'value': '250',
        'unit': 'μg/L',
        'color': Colors.red,
        'progress': 0.7
      },
      {
        'name': 'Vitamin B9',
        'value': '2.7',
        'unit': 'μg/mL',
        'color': Colors.teal,
        'progress': 0.4
      },
      {
        'name': 'Vitamin B12',
        'value': '197',
        'unit': 'μg/mL',
        'color': Colors.blue[800]!,
        'progress': 0.9
      },
    ];

    return Column(
      children: vitamins
          .map((vitamin) => _buildNutrientItem(
                name: vitamin['name'] as String,
                value: vitamin['value'] as String,
                unit: vitamin['unit'] as String,
                color: vitamin['color'] as Color,
                progress: vitamin['progress'] as double,
              ))
          .toList(),
    );
  }

  Widget _buildMineralsList(Map<String, dynamic>? latestReading) {
    final minerals = [
      {
        'name': 'Calcium (Total)',
        'value': '8.6',
        'unit': 'mg/dL',
        'color': Colors.green,
        'progress': 0.7
      },
      {
        'name': 'Iron (Serum)',
        'value': '60',
        'unit': 'μg/dL',
        'color': Colors.orange,
        'progress': 0.5
      },
      {
        'name': 'Ferritin',
        'value': 'Male 30',
        'unit': 'μg/mL',
        'color': Colors.cyan,
        'progress': 0.6
      },
      {
        'name': 'Magnesium',
        'value': '1.8',
        'unit': 'mg/dL',
        'color': Colors.blue,
        'progress': 0.4
      },
      {
        'name': 'Phosphorus',
        'value': '2.5',
        'unit': 'mg/dL',
        'color': Colors.red,
        'progress': 0.3
      },
      {
        'name': 'Zinc',
        'value': '70',
        'unit': 'μg/dL',
        'color': Colors.indigo,
        'progress': 0.8
      },
      {
        'name': 'Selenium',
        'value': '70',
        'unit': 'μg/L',
        'color': Colors.blue,
        'progress': 0.8
      },
      {
        'name': 'Copper',
        'value': 'Male 35',
        'unit': 'μg/dL',
        'color': Colors.green,
        'progress': 0.6
      },
      {
        'name': 'Manganese',
        'value': '4.7',
        'unit': 'μg/L',
        'color': Colors.yellow[700]!,
        'progress': 0.5
      },
    ];

    return Column(
      children: minerals
          .map((mineral) => _buildNutrientItem(
                name: mineral['name'] as String,
                value: mineral['value'] as String,
                unit: mineral['unit'] as String,
                color: mineral['color'] as Color,
                progress: mineral['progress'] as double,
              ))
          .toList(),
    );
  }

  Widget _buildNutrientItem({
    required String name,
    required String value,
    required String unit,
    required Color color,
    required double progress,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: MySize.size15),
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(MySize.size12),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
              Text(
                "$value $unit",
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
              ),
            ],
          ),
          Space.height(8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppColors.progressBackground.withAlpha(50),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: MySize.size10,
            borderRadius: BorderRadius.circular(MySize.size8),
          ),
        ],
      ),
    );
  }
}
