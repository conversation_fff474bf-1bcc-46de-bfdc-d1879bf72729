<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your storage for uploading files.</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Helthy</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>healo</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app needs local network access to debug and connect to Flutter services.</string>
	<key>NSHealthShareUsageDescription</key>
	<string>We will sync your data with the Apple Health app to give you better insights</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>We will sync your data with the Apple Health app to give you better insights</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>We need notification permissions to send you medication reminders and health updates</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>CFBundleURLTypes</key>
	<array>
	<dict>
		<key>CFBundleTypeRole</key>
		<string>Editor</string>
		<key>CFBundleURLSchemes</key>
		<array>
		<string>com.googleusercontent.apps.480554517469-h6i701cmcdvuo3fostpadgtt4rkpeph0</string>
		</array>
	</dict>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
	<dict>
		<key>CFBundleURLName</key>
		<string>fitbit-oauth</string>
		<key>CFBundleURLSchemes</key>
		<array>
		<string>com.techolab.helthy</string>
		</array>
	</dict>
	</array>
</dict>
</plist>
