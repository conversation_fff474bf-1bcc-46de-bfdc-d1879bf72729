import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/vitamin_provider.dart';
import 'package:healo/providers/mineral_provider.dart';
import 'package:healo/providers/vitamins_minerals_provider.dart';
import 'package:healo/widgets/add_vitamins_dialog.dart';
import 'package:healo/widgets/add_minerals_dialog.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class VitaminsMineralsScreen extends ConsumerStatefulWidget {
  const VitaminsMineralsScreen({super.key});

  @override
  ConsumerState<VitaminsMineralsScreen> createState() =>
      _VitaminsMineralsScreenState();
}

class _VitaminsMineralsScreenState
    extends ConsumerState<VitaminsMineralsScreen> {
  bool isVitaminsSelected = true;

  @override
  void initState() {
    super.initState();
    // Fetch vitamin and mineral data when screen loads
    Future.microtask(() {
      ref.read(vitaminHistoryProvider.notifier).fetchVitaminHistory();
      ref.read(vitaminIntakeProvider.notifier).fetchVitaminIntake();
      ref.read(mineralHistoryProvider.notifier).fetchMineralHistory();
      ref.read(vitaminsAndMineralsProvider.notifier).refreshData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final latestVitaminReading = ref.watch(latestVitaminReadingProvider);
    final latestMineralReading = ref.watch(latestMineralReadingProvider);
    final vitaminsAndMineralsState = ref.watch(vitaminsAndMineralsProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "Vitamins & Minerals",
        actions: [
          IconButton(
            onPressed: _showAddDialog,
            icon: Icon(
              Icons.add,
              color: AppColors.primaryColor,
              size: MySize.size24,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(MySize.size15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Nutrition Score Gauge
              _buildNutritionScoreGauge(),

              Space.height(20),

              // Recommendation Card
              _buildRecommendationCard(),

              Space.height(20),

              // Toggle Switch
              _buildToggleSwitch(),

              Space.height(20),

              // Nutrient Categories
              _buildNutrientCategories(latestVitaminReading),

              // Recommendations
              Space.height(20),
              _buildRecommendationsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNutritionScoreGauge() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size15),
        boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ],
      ),
      child: Column(
        children: [
          Text(
            "Nutrition Score",
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          Space.height(MySize.size30),
          SizedBox(
            height: MySize.size150,
            child: SfRadialGauge(
              enableLoadingAnimation: true,
              axes: <RadialAxis>[
                RadialAxis(
                  showLabels: false,
                  showTicks: false,
                  startAngle: 180,
                  endAngle: 0,
                  radiusFactor: 1.25,
                  minimum: 0,
                  maximum: 100,
                  axisLineStyle: AxisLineStyle(
                    thicknessUnit: GaugeSizeUnit.factor,
                    thickness: 0.25,
                    color: AppColors.progressBackground.withAlpha(50),
                  ),
                  pointers: <GaugePointer>[
                    RangePointer(
                      value: 78,
                      enableAnimation: true,
                      animationDuration: 1200,
                      sizeUnit: GaugeSizeUnit.factor,
                      color: AppColors.primaryColor,
                      width: 0.25,
                    ),
                  ],
                  annotations: <GaugeAnnotation>[
                    GaugeAnnotation(
                      angle: 90,
                      positionFactor: 0,
                      widget: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            "78",
                            style: TextStyle(
                              fontSize: MySize.size36,
                              fontWeight: FontWeight.bold,
                              color:
                                  Theme.of(context).textTheme.bodyLarge?.color,
                            ),
                          ),
                          Text(
                            "Good",
                            style: TextStyle(
                              fontSize: MySize.size16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size10),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: Color(0xFF10B981), // Green color
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: SvgPicture.asset( isVitaminsSelected ?
              "assets/svg/vitamin_icon.svg" : "assets/svg/mineral_icon.svg",
              height: MySize.size24,
              width: MySize.size24,
              colorFilter: ColorFilter.mode(
                Colors.white,
                BlendMode.srcIn,
              ),
            ),
          ),
          Space.width(12),
          Expanded(
            child: Text(
              isVitaminsSelected
                  ? "Your Vitamin D is Low. Try to Spend 15 mins in Sunlight Today"
                  : "Your Calcium level is low. Try adding a glass of milk, or dairy greens to your meals today",
              style: TextStyle(
                fontSize: MySize.size13,
                color: Theme.of(context).textTheme.bodySmall!.color,
                fontWeight: FontWeight.w500,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSwitch() {
    return Container(
      padding: EdgeInsets.all(MySize.size10),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size8),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isVitaminsSelected = true;
                });
              },
              child: AnimatedContainer(
                duration: Duration(milliseconds: 200),
                height: MySize.size45,
                decoration: BoxDecoration(
                  color: isVitaminsSelected
                      ? AppColors.primaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Center(
                  child: Text(
                    'Vitamins',
                    style: TextStyle(
                      color: isVitaminsSelected
                          ? Colors.white
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      fontWeight: FontWeight.w600,
                      fontSize: MySize.size14,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isVitaminsSelected = false;
                });
              },
              child: AnimatedContainer(
                duration: Duration(milliseconds: 200),
                height: MySize.size45,
                decoration: BoxDecoration(
                  color: !isVitaminsSelected
                      ? AppColors.primaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Center(
                  child: Text(
                    'Minerals',
                    style: TextStyle(
                      color: !isVitaminsSelected
                          ? Colors.white
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      fontWeight: FontWeight.w600,
                      fontSize: MySize.size14,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutrientCategories(Map<String, dynamic>? latestReading) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isVitaminsSelected ? "Nutrient Categories" : "Minerals Categories",
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        Space.height(15),
        if (isVitaminsSelected)
          _buildVitaminsList(latestReading)
        else
          _buildMineralsList(latestReading),
      ],
    );
  }

  Widget _buildVitaminsList(Map<String, dynamic>? latestReading) {
    final latestVitaminData = ref.watch(latestVitaminReadingProvider);
    final vitaminsData = latestVitaminData?['vitamins'] as Map<String, dynamic>?;

    // Helper function to get real value or fallback
    String getVitaminValue(String vitaminName, String fallbackValue) {
      return vitaminsData?[vitaminName]?.toString() ?? fallbackValue;
    }

    final vitamins = [
      {
        'name': 'Vitamin A',
        'value': getVitaminValue('Vitamin A', '400'),
        'unit': 'μg/L',
        'color': Colors.purple,
        'progress': 0.8
      },
      {
        'name': 'Vitamin B1',
        'value': getVitaminValue('Vitamin B1', '75'),
        'unit': 'μg/L',
        'color': Colors.orange,
        'progress': 0.6
      },
      {
        'name': 'Vitamin B2',
        'value': getVitaminValue('Vitamin B2', '1.4'),
        'unit': 'μg/L',
        'color': Colors.blue,
        'progress': 0.7
      },
      {
        'name': 'Vitamin B3',
        'value': getVitaminValue('Vitamin B3', '1.2'),
        'unit': 'μg/L',
        'color': Colors.cyan,
        'progress': 0.5
      },
      {
        'name': 'Vitamin B5',
        'value': getVitaminValue('Vitamin B5', '1.6'),
        'unit': 'μg/L',
        'color': Colors.green,
        'progress': 0.8
      },
      {
        'name': 'Vitamin B6',
        'value': getVitaminValue('Vitamin B6', '5-50'),
        'unit': 'μg/mL',
        'color': Colors.indigo,
        'progress': 0.6
      },
      {
        'name': 'Vitamin B7',
        'value': getVitaminValue('Vitamin B7', '250'),
        'unit': 'μg/L',
        'color': Colors.red,
        'progress': 0.7
      },
      {
        'name': 'Vitamin B9',
        'value': getVitaminValue('Vitamin B9', '2.7'),
        'unit': 'μg/mL',
        'color': Colors.teal,
        'progress': 0.4
      },
      {
        'name': 'Vitamin B12',
        'value': getVitaminValue('Vitamin B12', '197'),
        'unit': 'pg/mL',
        'color': Colors.blue[700]!,
        'progress': 0.8
      },
      {
        'name': 'Vitamin C',
        'value': getVitaminValue('Vitamin C', '0.8'),
        'unit': 'mL',
        'color': Colors.orange[600]!,
        'progress': 0.4
      },
      {
        'name': 'Vitamin D (25-OH)',
        'value': getVitaminValue('Vitamin D (25-OH)', '30'),
        'unit': 'ng/mL',
        'color': Colors.green[500]!,
        'progress': 0.6
      },
      {
        'name': 'Vitamin E',
        'value': getVitaminValue('Vitamin E', '5.5'),
        'unit': 'mg/L',
        'color': Colors.brown[600]!,
        'progress': 0.7
      },
      {
        'name': 'Vitamin K1',
        'value': getVitaminValue('Vitamin K1', '3.1'),
        'unit': 'ng/mL',
        'color': Colors.yellow[700]!,
        'progress': 0.5
      },
    ];

    return Column(
      children: [
        ...vitamins
            .map((vitamin) => _buildNutrientItem(
                  name: vitamin['name'] as String,
                  value: vitamin['value'] as String,
                  unit: vitamin['unit'] as String,
                  color: vitamin['color'] as Color,
                  progress: vitamin['progress'] as double,
                )),
      ],
    );
  }

  Widget _buildMineralsList(Map<String, dynamic>? latestReading) {
    final latestMineralData = ref.watch(latestMineralReadingProvider);
    final mineralsData = latestMineralData?['minerals'] as Map<String, dynamic>?;

    // Helper function to get real value or fallback
    String getMineralValue(String mineralName, String fallbackValue) {
      return mineralsData?[mineralName]?.toString() ?? fallbackValue;
    }

    final minerals = [
      {
        'name': 'Calcium (Total)',
        'value': getMineralValue('Calcium (Total)', '8.6'),
        'unit': 'mg/dL',
        'color': Colors.green,
        'progress': 0.7
      },
      {
        'name': 'Iron (Serum)',
        'value': getMineralValue('Iron (Serum)', '60'),
        'unit': 'μg/dL',
        'color': Colors.orange,
        'progress': 0.5
      },
      {
        'name': 'Ferritin',
        'value': getMineralValue('Ferritin', 'Male 30'),
        'unit': 'μg/mL',
        'color': Colors.cyan,
        'progress': 0.6
      },
      {
        'name': 'Magnesium',
        'value': getMineralValue('Magnesium', '1.8'),
        'unit': 'mg/dL',
        'color': Colors.blue,
        'progress': 0.4
      },
      {
        'name': 'Phosphorus',
        'value': getMineralValue('Phosphorus', '2.5'),
        'unit': 'mg/dL',
        'color': Colors.red,
        'progress': 0.3
      },
      {
        'name': 'Zinc',
        'value': getMineralValue('Zinc', '70'),
        'unit': 'μg/dL',
        'color': Colors.indigo,
        'progress': 0.8
      },
      {
        'name': 'Selenium',
        'value': getMineralValue('Selenium', '70'),
        'unit': 'μg/L',
        'color': Colors.blue,
        'progress': 0.8
      },
      {
        'name': 'Copper',
        'value': getMineralValue('Copper', 'Male 35'),
        'unit': 'μg/dL',
        'color': Colors.green,
        'progress': 0.6
      },
      {
        'name': 'Manganese',
        'value': getMineralValue('Manganese', '4.7'),
        'unit': 'μg/L',
        'color': Colors.green[600]!,
        'progress': 0.7
      },
      {
        'name': 'Iodine(urine)',
        'value': getMineralValue('Iodine(urine)', '120'),
        'unit': 'μg/L',
        'color': Colors.yellow[700]!,
        'progress': 0.6
      },
      {
        'name': 'Sodium(Na+)',
        'value': getMineralValue('Sodium(Na+)', '135'),
        'unit': 'mmol/L',
        'color': Colors.purple[600]!,
        'progress': 0.8
      },
      {
        'name': 'Potassium(K+)',
        'value': getMineralValue('Potassium(K+)', '3.5'),
        'unit': 'mmol/L',
        'color': Colors.red[500]!,
        'progress': 0.4
      },
      {
        'name': 'Chloride (cl-)',
        'value': getMineralValue('Chloride (cl-)', '98'),
        'unit': 'mmol/L',
        'color': Colors.blue[600]!,
        'progress': 0.7
      },
      {
        'name': 'Fluoride',
        'value': getMineralValue('Fluoride', '1.5'),
        'unit': 'mg/L',
        'color': Colors.teal[700]!,
        'progress': 0.8
      },
      {
        'name': 'Chromium',
        'value': getMineralValue('Chromium', '0.2'),
        'unit': 'μg/L',
        'color': Colors.purple[400]!,
        'progress': 0.9
      },
      {
        'name': 'Molybdenum',
        'value': getMineralValue('Molybdenum', '0.45'),
        'unit': 'μg/L',
        'color': Colors.orange[600]!,
        'progress': 0.5
      },
    ];

    return Column(
      children: [
        ...minerals
            .map((mineral) => _buildNutrientItem(
                  name: mineral['name'] as String,
                  value: mineral['value'] as String,
                  unit: mineral['unit'] as String,
                  color: mineral['color'] as Color,
                  progress: mineral['progress'] as double,
                )),
      ],
    );
  }

  Widget _buildRecommendationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Recommendations",
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        Space.height(15),
        _buildFoodRecommendationCard(
          icon: isVitaminsSelected ? Icons.wb_sunny_outlined : Icons.water_drop_outlined,
          title: "Salmon",
          description: "Rich in Omega-3 and Vitamin D",
        ),
        Space.height(15),
        _buildFoodRecommendationCard(
          icon: Icons.eco_outlined,
          title: "Spinach Salad",
          description: "Include more leafy greens and omega-3 rich foods",
        ),
      ],
    );
  }

  Widget _buildFoodRecommendationCard({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(MySize.size10),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(2, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(MySize.size10),
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: MySize.size24,
            ),
          ),
          SizedBox(width: MySize.size15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: MySize.size16,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutrientItem({
    required String name,
    required String value,
    required String unit,
    required Color color,
    required double progress,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: MySize.size15),
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(MySize.size12),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 6),
            ),
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
              Text(
                "$value $unit",
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
              ),
            ],
          ),
          Space.height(8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withAlpha(50),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: MySize.size10,
            borderRadius: BorderRadius.circular(MySize.size8),
          ),
        ],
      ),
    );
  }

  void _showAddDialog() {
    if (isVitaminsSelected) {
      showDialog(
        context: context,
        builder: (context) => const AddVitaminsDialog(),
      );
    } else {
      showDialog(
        context: context,
        builder: (context) => const AddMineralsDialog(),
      );
    }
  }
}
