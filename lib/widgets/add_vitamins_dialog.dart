import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/vitamins_minerals_provider.dart';

class AddVitaminsDialog extends ConsumerStatefulWidget {
  const AddVitaminsDialog({super.key});

  @override
  ConsumerState<AddVitaminsDialog> createState() => _AddVitaminsDialogState();
}

class _AddVitaminsDialogState extends ConsumerState<AddVitaminsDialog> {
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, String> _vitaminUnits = {
    'Vitamin A': 'μg/L',
    'Vitamin B1': 'μg/L',
    'Vitamin B2': 'μg/L',
    'Vitamin B3': 'μg/L',
    'Vitamin B5': 'μg/L',
    'Vitamin B6': 'ng/mL',
    'Vitamin B7': 'ng/mL',
    'Vitamin B9': 'ng/mL',
    'Vitamin B12': 'pg/mL',
    'Vitamin C': 'mg/L',
    'Vitamin D (25-OH)': 'pg/mL',
    'Vitamin K1': 'ng/mL',
  };

  @override
  void initState() {
    super.initState();
    for (String vitamin in _vitaminUnits.keys) {
      _controllers[vitamin] = TextEditingController();
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(vitaminsAndMineralsProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(MySize.size16),
      ),
      child: Container(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: EdgeInsets.all(MySize.size20),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Add Vitamins Values",
              style: TextStyle(
                fontSize: MySize.size20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            SizedBox(height: MySize.size20),

            // Vitamins input fields in a grid
            Expanded(
              child: GridView.builder(
                padding: EdgeInsets.zero,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 2.2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 20,
                ),
                itemCount: _vitaminUnits.length,
                itemBuilder: (context, index) {
                  final vitamin = _vitaminUnits.keys.elementAt(index);
                  final unit = _vitaminUnits[vitamin]!;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        vitamin,
                        style: TextStyle(
                          fontSize: MySize.size14,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                      SizedBox(height: MySize.size8),
                      Container(
                        height: MySize.size36,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey.shade300,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(MySize.size8),
                        ),
                        child: TextField(
                          controller: _controllers[vitamin],
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: MySize.size14),
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: MySize.size8,
                              vertical: MySize.size8,
                            ),
                            suffixText: unit,
                            suffixStyle: TextStyle(
                              fontSize: MySize.size12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),

            SizedBox(height: MySize.size24),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: MySize.size12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size25),
                      ),
                      side: BorderSide(color: Colors.grey.shade400),
                    ),
                    child: Text(
                      "Cancel",
                      style: TextStyle(
                        fontSize: MySize.size16,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: MySize.size16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: state.isLoading ? null : _saveVitamins,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      padding: EdgeInsets.symmetric(vertical: MySize.size12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size25),
                      ),
                    ),
                    child: state.isLoading
                        ? SizedBox(
                            height: MySize.size20,
                            width: MySize.size20,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            "Save Results",
                            style: TextStyle(
                              fontSize: MySize.size16,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _saveVitamins() async {
    try {
      final vitaminData = <String, dynamic>{};

      for (String vitamin in _vitaminUnits.keys) {
        final value = _controllers[vitamin]?.text.trim();
        if (value != null && value.isNotEmpty) {
          vitaminData[vitamin] = value;
        }
      }

      if (vitaminData.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Please enter at least one vitamin value"),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      await ref.read(vitaminsAndMineralsProvider.notifier).addVitaminReading(vitaminData);

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Vitamin values saved successfully!"),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Error saving vitamin values: $e"),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
