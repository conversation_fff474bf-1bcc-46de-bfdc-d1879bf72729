import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/vitamin_provider.dart';
import 'package:healo/providers/mineral_provider.dart';
import 'package:healo/services/firestore_service.dart';

// Combined provider for vitamins and minerals screen
final vitaminsAndMineralsProvider = StateNotifierProvider<VitaminsAndMineralsNotifier, VitaminsAndMineralsState>(
  (ref) => VitaminsAndMineralsNotifier(FirestoreService(), ref),
);

class VitaminsAndMineralsState {
  final bool isLoading;
  final String? error;
  final Map<String, dynamic>? latestVitaminReading;
  final Map<String, dynamic>? latestMineralReading;

  VitaminsAndMineralsState({
    this.isLoading = false,
    this.error,
    this.latestVitaminReading,
    this.latestMineralReading,
  });

  VitaminsAndMineralsState copyWith({
    bool? isLoading,
    String? error,
    Map<String, dynamic>? latestVitaminReading,
    Map<String, dynamic>? latestMineralReading,
  }) {
    return VitaminsAndMineralsState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      latestVitaminReading: latestVitaminReading ?? this.latestVitaminReading,
      latestMineralReading: latestMineralReading ?? this.latestMineralReading,
    );
  }
}

class VitaminsAndMineralsNotifier extends StateNotifier<VitaminsAndMineralsState> {
  final Ref _ref;

  VitaminsAndMineralsNotifier(FirestoreService firestoreService, this._ref) : super(VitaminsAndMineralsState()) {
    _initializeData();
  }

  void _initializeData() {
    // Listen to vitamin and mineral data changes
    _ref.listen(latestVitaminReadingProvider, (previous, next) {
      state = state.copyWith(latestVitaminReading: next);
    });

    _ref.listen(latestMineralReadingProvider, (previous, next) {
      state = state.copyWith(latestMineralReading: next);
    });
  }

  // Add vitamin reading
  Future<void> addVitaminReading(Map<String, dynamic> vitaminData) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final now = DateTime.now();
      final dateKey = "${now.day}-${now.month}-${now.year}";

      final readingData = {
        'timestamp': now.toIso8601String(),
        'vitamins': vitaminData,
      };

      await _ref.read(vitaminHistoryProvider.notifier).addVitaminReading(dateKey, readingData);

      state = state.copyWith(isLoading: false);
      log("Vitamin reading added successfully");
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      log("Error adding vitamin reading: $e");
      rethrow;
    }
  }

  // Add mineral reading
  Future<void> addMineralReading(Map<String, dynamic> mineralData) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final now = DateTime.now();
      final dateKey = "${now.day}-${now.month}-${now.year}";

      final readingData = {
        'timestamp': now.toIso8601String(),
        'minerals': mineralData,
      };

      await _ref.read(mineralHistoryProvider.notifier).addMineralReading(dateKey, readingData);

      state = state.copyWith(isLoading: false);
      log("Mineral reading added successfully");
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      log("Error adding mineral reading: $e");
      rethrow;
    }
  }

  // Refresh all data
  Future<void> refreshData() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await Future.wait([
        _ref.read(vitaminHistoryProvider.notifier).fetchVitaminHistory(),
        _ref.read(mineralHistoryProvider.notifier).fetchMineralHistory(),
      ]);

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      log("Error refreshing data: $e");
    }
  }

  // Get vitamin value by name from latest reading
  String? getVitaminValue(String vitaminName) {
    final latestReading = state.latestVitaminReading;
    if (latestReading == null) return null;

    final vitamins = latestReading['vitamins'] as Map<String, dynamic>?;
    if (vitamins == null) return null;

    return vitamins[vitaminName]?.toString();
  }

  // Get mineral value by name from latest reading
  String? getMineralValue(String mineralName) {
    final latestReading = state.latestMineralReading;
    if (latestReading == null) return null;

    final minerals = latestReading['minerals'] as Map<String, dynamic>?;
    if (minerals == null) return null;

    return minerals[mineralName]?.toString();
  }
}
