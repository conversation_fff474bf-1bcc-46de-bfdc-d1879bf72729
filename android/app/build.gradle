// Use "plugins" block instead of "apply plugin"
plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

// FIX #1: Use 'def' instead of 'val' for variable declaration in Groovy.
// It is also good practice to use 'new' when creating object instances.
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file("key.properties")

if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.techolab.helthy"
    compileSdk = 35 // Consider using flutter.compileSdkVersion if defined in your project
    ndkVersion = flutter.ndkVersion

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    // FIX #2 & #3: Define signingConfigs FIRST, and use Groovy-compatible syntax.
    // The ?.let syntax is from Kotlin and will not work here.
    signingConfigs {
        release {
            if (keystorePropertiesFile.exists()) {
                keyAlias = keystoreProperties['keyAlias']
                keyPassword = keystoreProperties['keyPassword']
                storePassword = keystoreProperties['storePassword']
                
                // Use a simple 'if' check, which is valid Groovy syntax.
                if (keystoreProperties['storeFile'] != null) {
                    storeFile = file(keystoreProperties['storeFile'])
                }
            }
        }
    }

    defaultConfig {
        applicationId = "com.techolab.helthy"
        minSdkVersion 26
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled true
    }

    // Configure buildTypes SECOND, so it can find the 'signingConfigs.release' defined above.
    buildTypes {
        release {
            // This now correctly uses the signing config defined above.
            signingConfig = signingConfigs.release
            // For a release build, you should enable code shrinking and obfuscation.
            // minifyEnabled true
            // shrinkResources true
            // proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation(files("libs/samsung-health-data-api-1.0.0-b2.aar"))  // Samsung Health SDK
    implementation platform('com.google.firebase:firebase-bom:32.1.0')  // Firebase BOM
    implementation 'com.google.firebase:firebase-auth'                  // Firebase Auth

    // Samsung Health SDK dependencies
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core-ktx:1.10.1'

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
}